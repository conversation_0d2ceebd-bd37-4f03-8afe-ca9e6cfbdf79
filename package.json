{"name": "loan_calc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"chart.js": "^4.5.0", "next": "15.4.2", "next-pwa": "^5.6.0", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/next-pwa": "^5.6.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}