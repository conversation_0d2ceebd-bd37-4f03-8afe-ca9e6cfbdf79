import { CreditLoanInput, CreditLoanResult, MonthlyPayment } from '../types/creditLoan';

/**
 * 計算每月利率
 * @param annualRate 年利率 (%)
 * @returns 月利率 (小數)
 */
function getMonthlyRate(annualRate: number): number {
  return annualRate / 100 / 12;
}

/**
 * 計算等額本息還款的月付金
 * @param principal 本金
 * @param monthlyRate 月利率
 * @param months 期數
 * @returns 月付金
 */
function calculateEqualPayment(principal: number, monthlyRate: number, months: number): number {
  if (monthlyRate === 0) {
    return principal / months;
  }

  const factor = Math.pow(1 + monthlyRate, months);
  return principal * (monthlyRate * factor) / (factor - 1);
}

/**
 * 根據利率類型獲取每月利率
 * @param input 信貸輸入參數
 * @param month 當前月份 (1-based)
 * @returns 當月年利率 (%)
 */
function getMonthlyAnnualRate(input: CreditLoanInput, month: number): number {
  switch (input.rateType) {
    case 'single':
      return input.singleRate || 0;

    case 'two-stage':
      if (!input.twoStageRates) return 0;
      if (month <= input.twoStageRates.firstStage.months) {
        return input.twoStageRates.firstStage.rate;
      }
      return input.twoStageRates.secondStage.rate;

    case 'three-stage':
      if (!input.threeStageRates) return 0;
      if (month <= input.threeStageRates.firstStage.months) {
        return input.threeStageRates.firstStage.rate;
      }
      if (month <= input.threeStageRates.firstStage.months + input.threeStageRates.secondStage.months) {
        return input.threeStageRates.secondStage.rate;
      }
      return input.threeStageRates.thirdStage.rate;

    default:
      return 0;
  }
}

/**
 * 計算信貸還款計劃
 * @param input 信貸輸入參數
 * @returns 信貸計算結果
 */
export function calculateCreditLoan(input: CreditLoanInput): CreditLoanResult {
  const monthlyPayments: MonthlyPayment[] = [];
  let remainingBalance = input.loanAmount;

  // 先計算標準的等額本息月付金，並無條件進位作為固定月付金
  const firstMonthRate = getMonthlyRate(getMonthlyAnnualRate(input, 1));
  const standardMonthlyPayment = calculateEqualPayment(input.loanAmount, firstMonthRate, input.termMonths);
  const fixedMonthlyPayment = Math.ceil(standardMonthlyPayment);

  // 計算每月還款明細
  for (let month = 1; month <= input.termMonths; month++) {
    const annualRate = getMonthlyAnnualRate(input, month);
    const monthlyRate = getMonthlyRate(annualRate);

    // 計算當月利息
    const interest = remainingBalance * monthlyRate;

    // 計算當月應還本金
    let principal: number;
    let totalPayment: number;

    if (month === input.termMonths) {
      // 最後一期，還清剩餘本金
      principal = remainingBalance;
      totalPayment = interest + principal;
    } else {
      // 前面各期使用固定月付金
      totalPayment = fixedMonthlyPayment;
      principal = totalPayment - interest;

      // 確保本金不為負數
      if (principal < 0) {
        principal = 0;
        totalPayment = interest;
      }

      // 確保本金不超過剩餘本金
      if (principal > remainingBalance) {
        principal = remainingBalance;
        totalPayment = interest + principal;
      }
    }

    const monthlyFee = input.fees.monthlyFee;
    const totalMonthlyPayment = totalPayment + monthlyFee;

    monthlyPayments.push({
      month,
      interest: Math.round(interest),
      principal: Math.round(principal),
      totalPayment: Math.round(totalPayment),
      remainingBalance: remainingBalance - principal,
      monthlyFee,
      totalMonthlyPayment
    });

    remainingBalance -= principal;
  }

  // 調整最後一期，確保總本金等於貸款金額
  const lastPayment = monthlyPayments[monthlyPayments.length - 1];
  const previousPrincipalSum = monthlyPayments.slice(0, -1).reduce((sum, payment) => sum + payment.principal, 0);
  const correctLastPrincipal = input.loanAmount - previousPrincipalSum;

  lastPayment.principal = correctLastPrincipal;
  lastPayment.totalPayment = lastPayment.interest + lastPayment.principal;
  lastPayment.totalMonthlyPayment = lastPayment.totalPayment + lastPayment.monthlyFee;
  lastPayment.remainingBalance = 0;

  // 重新計算總利息（無條件捨去）
  const totalInterestBeforeFloor = monthlyPayments.reduce((sum, payment) => sum + payment.interest, 0);
  const totalInterest = Math.floor(totalInterestBeforeFloor);

  const totalPrincipal = input.loanAmount;
  const totalMonthlyFees = input.fees.monthlyFee * input.termMonths;
  const totalFees = input.fees.setupFee + totalMonthlyFees + input.fees.otherFees;
  const totalAmount = totalPrincipal + totalInterest + totalFees;
  const averageMonthlyPayment = monthlyPayments.reduce((sum, payment) => sum + payment.totalMonthlyPayment, 0) / input.termMonths;
  // 抓第二期的月付金
  const monthlyPayment = monthlyPayments[1].totalMonthlyPayment;
  const rateType = input.rateType;


  // 計算總費用年百分利 (APR) - 簡化計算
  const effectiveAnnualRate = calculateAPR(input, totalAmount);


  return {
    monthlyPayments,
    summary: {
      totalInterest,
      totalPrincipal,
      totalFees,
      totalAmount,
      effectiveAnnualRate,
      averageMonthlyPayment,
      monthlyPayment,
      rateType
    }
  };
}

/**
 * 計算總費用年百分利 (APR)
 * @param input 信貸輸入參數
 * @param totalAmount 總還款金額（每月還款金額 * 期數，不含額外費用）
 * @returns 實際年利率 (%)
 */
export function calculateAPR(input: CreditLoanInput, totalAmount: number): number {
  const { loanAmount, termMonths, fees } = input;

  // 計算每月還款金額（不含帳管費，但 totalAmount 中不含費用）
  const monthlyPayment = totalAmount / termMonths;

  // 加回帳管費，才是實際每月支出金額
  const actualMonthlyOutflow = monthlyPayment + fees.monthlyFee;

  // 現金流列表：第0期為拿到手的淨金額，其餘每期為支出
  const netReceived = loanAmount - fees.setupFee - fees.otherFees;
  const cashflows: number[] = [netReceived];

  for (let i = 1; i <= termMonths; i++) {
    cashflows.push(-actualMonthlyOutflow);
  }

  // 計算月利率
  const monthlyRate = computeIRR(cashflows);

  // 換算成年利率 (APR)
  const apr = (Math.pow(1 + monthlyRate, 12) - 1) * 100;
  return parseFloat(apr.toFixed(3));
}

/**
 * 用牛頓法計算 IRR（每期報酬率）
 */
function computeIRR(cashflows: number[], guess = 0.01): number {
  const maxIterations = 1000;
  const tolerance = 1e-7;
  let rate = guess;

  for (let i = 0; i < maxIterations; i++) {
    let npv = 0;
    let derivative = 0;

    for (let t = 0; t < cashflows.length; t++) {
      npv += cashflows[t] / Math.pow(1 + rate, t);
      if (t > 0) {
        derivative -= t * cashflows[t] / Math.pow(1 + rate, t + 1);
      }
    }

    const newRate = rate - npv / derivative;
    if (Math.abs(newRate - rate) < tolerance) {
      return newRate;
    }
    rate = newRate;
  }

  throw new Error("IRR did not converge");
}



/**
 * 驗證信貸輸入參數
 * @param input 信貸輸入參數
 * @returns 驗證錯誤訊息
 */
export function validateCreditLoanInput(input: Partial<CreditLoanInput>): string[] {
  const errors: string[] = [];

  if (!input.loanAmount || input.loanAmount <= 0) {
    errors.push('貸款金額必須大於 0');
  }

  if (!input.termMonths || input.termMonths <= 0 || input.termMonths > 360) {
    errors.push('期數必須在 1-360 個月之間');
  }

  if (!input.rateType) {
    errors.push('請選擇利率計算方式');
  }

  // 驗證利率設定
  switch (input.rateType) {
    case 'single':
      if (!input.singleRate || input.singleRate <= 0) {
        errors.push('單一利率必須大於 0');
      }
      break;

    case 'two-stage':
      if (!input.twoStageRates) {
        errors.push('請設定 2 段式利率');
      } else {
        if (!input.twoStageRates.firstStage.months || input.twoStageRates.firstStage.months <= 0) {
          errors.push('第一段期數必須大於 0');
        }
        if (!input.twoStageRates.firstStage.rate || input.twoStageRates.firstStage.rate <= 0) {
          errors.push('第一段利率必須大於 0');
        }
        if (!input.twoStageRates.secondStage.rate || input.twoStageRates.secondStage.rate <= 0) {
          errors.push('第二段利率必須大於 0');
        }
        if (input.termMonths && input.twoStageRates.firstStage.months >= input.termMonths) {
          errors.push('第一段期數不能大於等於總期數');
        }
      }
      break;

    case 'three-stage':
      if (!input.threeStageRates) {
        errors.push('請設定 3 段式利率');
      } else {
        const { firstStage, secondStage, thirdStage } = input.threeStageRates;

        if (!firstStage.months || firstStage.months <= 0) {
          errors.push('第一段期數必須大於 0');
        }
        if (!firstStage.rate || firstStage.rate <= 0) {
          errors.push('第一段利率必須大於 0');
        }
        if (!secondStage.months || secondStage.months <= 0) {
          errors.push('第二段期數必須大於 0');
        }
        if (!secondStage.rate || secondStage.rate <= 0) {
          errors.push('第二段利率必須大於 0');
        }
        if (!thirdStage.rate || thirdStage.rate <= 0) {
          errors.push('第三段利率必須大於 0');
        }

        if (input.termMonths) {
          const totalFirstTwoStages = firstStage.months + secondStage.months;
          if (totalFirstTwoStages >= input.termMonths) {
            errors.push('前兩段期數總和不能大於等於總期數');
          }
        }
      }
      break;
  }

  return errors;
}
