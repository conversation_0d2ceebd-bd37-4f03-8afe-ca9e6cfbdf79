'use client';

import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Tag } from 'primereact/tag';
import { ProgressBar } from 'primereact/progressbar';
import { RateReverseResult } from '@/lib/types/creditLoan';
import { formatCurrency, formatNumber, roundTo } from '@/lib/calculations/utils';

interface RateReverseResultProps {
  result: RateReverseResult | null;
  loading?: boolean;
}

export default function RateReverseResultComponent({ result, loading = false }: RateReverseResultProps) {
  if (!result) {
    return (
      <Card className="w-full">
        <div className="text-center py-8">
          <i className="pi pi-search text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-500 text-lg">利率計算結果將顯示在這裡</p>
        </div>
      </Card>
    );
  }

  if (!result.convergence.success) {
    return (
      <Card className="w-full">
        <div className="text-center py-8">
          <i className="pi pi-exclamation-triangle text-4xl text-red-400 mb-4"></i>
          <p className="text-red-600 text-lg font-medium mb-2">無法計算出合理利率</p>
          <p className="text-gray-600 text-sm">
            請檢查輸入的月付金額是否合理，或調整貸款條件後重新計算
          </p>
          <div className="mt-4 text-xs text-gray-500">
            迭代次數: {result.convergence.iterations} | 精確度: {result.convergence.accuracy.toExponential(2)}
          </div>
        </div>
      </Card>
    );
  }

  // 獲取利率評估的顏色
  const getRateTagSeverity = (level: string) => {
    switch (level) {
      case 'low': return 'success';
      case 'medium': return 'info';
      case 'high': return 'warning';
      case 'very-high': return 'danger';
      default: return 'info';
    }
  };

  // 計算各項比例
  const totalAmount = result.totalAmount;
  const principalRatio = totalAmount > 0 ? (totalAmount - result.totalInterest - result.totalFees) / totalAmount * 100 : 0;
  const interestRatio = totalAmount > 0 ? result.totalInterest / totalAmount * 100 : 0;
  const feesRatio = totalAmount > 0 ? result.totalFees / totalAmount * 100 : 0;

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 主要結果 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 名目年利率 */}
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-800 mb-2">名目年利率</h3>
                <p className="text-3xl font-bold text-blue-900">
                  {formatNumber(result.nominalRate, 3)}%
                </p>
                <div className="mt-2">
                  <Tag
                    value={result.rateAssessment.description}
                    severity={getRateTagSeverity(result.rateAssessment.level)}
                  />
                </div>
              </div>
              <i className="pi pi-percentage text-4xl text-blue-600"></i>
            </div>
          </div>

          {/* 實際年利率 APR */}
          <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-lg border border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-purple-800 mb-2">實際年利率 (APR)</h3>
                <p className="text-3xl font-bold text-purple-900">
                  {formatNumber(result.effectiveRate, 3)}%
                </p>
                <p className="text-sm text-purple-700 mt-1">
                  包含所有費用成本
                </p>
              </div>
              <i className="pi pi-chart-line text-4xl text-purple-600"></i>
            </div>
          </div>
        </div>

        <Divider />

        {/* 詳細統計 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 總利息 */}
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-red-600 text-sm mb-2">總利息支出</div>
            <div className="text-xl font-bold text-red-700">
              {formatCurrency(result.totalInterest)}
            </div>
            <div className="text-xs text-red-500 mt-1">
              佔比 {formatNumber(interestRatio, 1)}%
            </div>
          </div>

          {/* 總費用 */}
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-orange-600 text-sm mb-2">總費用支出</div>
            <div className="text-xl font-bold text-orange-700">
              {formatCurrency(result.totalFees)}
            </div>
            <div className="text-xs text-orange-500 mt-1">
              佔比 {formatNumber(feesRatio, 1)}%
            </div>
          </div>

          {/* 總還款金額 */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-gray-600 text-sm mb-2">總還款金額</div>
            <div className="text-xl font-bold text-gray-800">
              {formatCurrency(result.totalAmount)}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              本金+利息+費用
            </div>
          </div>

          {/* 利率差異 */}
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-yellow-600 text-sm mb-2">APR 差異</div>
            <div className="text-xl font-bold text-yellow-700">
              +{formatNumber(result.effectiveRate - result.nominalRate, 3)}%
            </div>
            <div className="text-xs text-yellow-500 mt-1">
              費用影響
            </div>
          </div>
        </div>

        <Divider />

        {/* 成本結構分析 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">成本結構分析</h3>

          {/* 本金比例 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">本金</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(result.totalAmount - result.totalInterest - result.totalFees)} ({formatNumber(principalRatio, 1)}%)
              </span>
            </div>
            <ProgressBar
              value={roundTo(principalRatio, 2)}
              className="h-3"
              color="#3B82F6"
            />
          </div>

          {/* 利息比例 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">利息</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(result.totalInterest)} ({formatNumber(interestRatio, 1)}%)
              </span>
            </div>
            <ProgressBar
              value={roundTo(interestRatio, 2)}
              className="h-3"
              color="#EF4444"
            />
          </div>

          {/* 費用比例 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">費用</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(result.totalFees)} ({formatNumber(feesRatio, 1)}%)
              </span>
            </div>
            <ProgressBar
              value={roundTo(feesRatio, 2)}
              className="h-3"
              color="#F97316"
            />
          </div>
        </div>

        <Divider />

        {/* 計算資訊 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-800 mb-3">計算資訊</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-green-600 font-medium">收斂狀態</div>
              <div className="text-lg font-bold text-green-800">
                {result.convergence.success ? '成功' : '失敗'}
              </div>
              <div className="text-xs text-green-600 mt-1">
                計算狀態
              </div>
            </div>
            <div className="text-center">
              <div className="text-green-600 font-medium">迭代次數</div>
              <div className="text-lg font-bold text-green-800">
                {result.convergence.iterations}
              </div>
              <div className="text-xs text-green-600 mt-1">
                計算步驟
              </div>
            </div>
            <div className="text-center">
              <div className="text-green-600 font-medium">計算精度</div>
              <div className="text-lg font-bold text-green-800">
                {result.convergence.accuracy < 1e-6 ? '高精度' : '中精度'}
              </div>
              <div className="text-xs text-green-600 mt-1">
                {result.convergence.accuracy.toExponential(2)}
              </div>
            </div>
          </div>
        </div>

        {/* 重要提醒 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="pi pi-info-circle text-yellow-600 mt-1"></i>
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-2">重要提醒：</p>
              <ul className="space-y-1 text-xs">
                <li>• 此計算結果基於等額本息還款方式</li>
                <li>• 實際年利率(APR)已包含所有相關費用</li>
                <li>• 計算結果僅供參考，實際條件請以合約為準</li>
                <li>• 建議與其他貸款方案比較APR來選擇最優方案</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 利率比較指標 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-3">利率比較指標</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="text-center">
              <div className="text-blue-600 font-medium">市場水準</div>
              <div className="text-lg font-bold text-blue-800">
                {result.rateAssessment.level === 'low' ? '優於市場' :
                  result.rateAssessment.level === 'medium' ? '符合市場' :
                    result.rateAssessment.level === 'high' ? '高於市場' : '遠高於市場'}
              </div>
              <div className="text-xs text-blue-600 mt-1">
                相對評估
              </div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-medium">費用影響</div>
              <div className="text-lg font-bold text-blue-800">
                {result.effectiveRate - result.nominalRate < 1 ? '影響較小' :
                  result.effectiveRate - result.nominalRate < 2 ? '影響適中' : '影響較大'}
              </div>
              <div className="text-xs text-blue-600 mt-1">
                APR vs 名目利率
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
