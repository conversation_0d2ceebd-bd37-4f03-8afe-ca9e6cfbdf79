'use client';

import { useState } from 'react';
import { InputNumber } from 'primereact/inputnumber';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Message } from 'primereact/message';
import { RateReverseInput } from '@/lib/types/creditLoan';
import { validateRateReverseInput } from '@/lib/calculations/rateReverse';

interface RateReverseFormProps {
  onCalculate: (input: RateReverseInput) => void;
  loading?: boolean;
}

export default function RateReverseForm({ onCalculate, loading = false }: RateReverseFormProps) {
  const [formData, setFormData] = useState<RateReverseInput>({
    loanAmount: 1000000,
    monthlyPayment: 20000,
    termMonths: 84,
    fees: {
      setupFee: 0,
      monthlyFee: 0,
      otherFees: 0
    }
  });

  const [errors, setErrors] = useState<string[]>([]);

  const handleSubmit = () => {
    const validationErrors = validateRateReverseInput(formData);
    setErrors(validationErrors);

    if (validationErrors.length === 0) {
      onCalculate(formData);
    }
  };

  const updateFormData = (field: string, value: number | null | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value || 0
    }));
  };

  const updateNestedFormData = (parentField: string, childField: string, value: number | null | undefined) => {
    setFormData(prev => ({
      ...prev,
      [parentField]: {
        ...prev[parentField as keyof RateReverseInput],
        [childField]: value || 0
      }
    }));
  };

  // 計算基本統計
  const totalPayments = formData.monthlyPayment * formData.termMonths;
  const totalFees = formData.fees.setupFee + formData.fees.monthlyFee * formData.termMonths + formData.fees.otherFees;
  const estimatedInterest = totalPayments - formData.loanAmount - totalFees;


  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 功能說明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="pi pi-info-circle text-blue-600 mt-1"></i>
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">利率回推功能</p>
              <p>輸入已知的貸款條件，系統將自動計算出對應的利率和APR</p>
            </div>
          </div>
        </div>

        {/* 錯誤訊息 */}
        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <Message key={index} severity="error" text={error} className="w-full" />
            ))}
          </div>
        )}

        {/* 基本資訊 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">貸款條件</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="field">
              <label htmlFor="loanAmount" className="block text-sm font-medium mb-2">
                貸款金額 (元)
              </label>
              <InputNumber
                id="loanAmount"
                value={formData.loanAmount}
                onValueChange={(e) => updateFormData('loanAmount', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={10000}
                max={50000000}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label htmlFor="monthlyPayment" className="block text-sm font-medium mb-2">
                月付金額 (元)
              </label>
              <InputNumber
                id="monthlyPayment"
                value={formData.monthlyPayment}
                onValueChange={(e) => updateFormData('monthlyPayment', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={100}
                max={1000000}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>
          </div>

          <div className="field">
            <label htmlFor="termMonths" className="block text-sm font-medium mb-2">
              期數 (月)
            </label>
            <InputNumber
              id="termMonths"
              value={formData.termMonths}
              onValueChange={(e) => updateFormData('termMonths', e.value)}
              min={1}
              max={360}
              className="w-full"
              suffix=" 個月"
            />
          </div>
        </div>

        <Divider />

        {/* 相關費用 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">相關費用</h3>

          <div className="flex-items gap-2">
            <div className="field">
              <label className="block text-sm font-medium mb-2">
                開辦費 (元)
              </label>
              <InputNumber
                value={formData.fees.setupFee}
                onValueChange={(e) => updateNestedFormData('fees', 'setupFee', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                帳管費 (每月)
              </label>
              <InputNumber
                value={formData.fees.monthlyFee}
                onValueChange={(e) => updateNestedFormData('fees', 'monthlyFee', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                其他費用 (元)
              </label>
              <InputNumber
                value={formData.fees.otherFees}
                onValueChange={(e) => updateNestedFormData('fees', 'otherFees', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>
          </div>
        </div>

        <Divider />

        {/* 預估資訊 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-3">預估資訊</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="text-gray-600">總還款金額</div>
              <div className="text-lg font-bold text-gray-800">
                {new Intl.NumberFormat('zh-TW', {
                  style: 'currency',
                  currency: 'TWD',
                  minimumFractionDigits: 0
                }).format(totalPayments)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-600">總費用</div>
              <div className="text-lg font-bold text-orange-600">
                {new Intl.NumberFormat('zh-TW', {
                  style: 'currency',
                  currency: 'TWD',
                  minimumFractionDigits: 0
                }).format(totalFees)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-600">預估利息</div>
              <div className="text-lg font-bold text-red-600">
                {new Intl.NumberFormat('zh-TW', {
                  style: 'currency',
                  currency: 'TWD',
                  minimumFractionDigits: 0
                }).format(Math.max(0, estimatedInterest))}
              </div>
            </div>
          </div>
        </div>

        {/* 計算按鈕 */}
        <div className="flex justify-center pt-4">
          <Button
            label="計算利率"
            icon="pi pi-calculator"
            onClick={handleSubmit}
            loading={loading}
            size="large"
            className="px-8"
          />
        </div>
      </div>
    </Card>
  );
}
